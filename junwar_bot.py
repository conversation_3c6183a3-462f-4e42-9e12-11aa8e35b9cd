import os
import logging
from docx import Document
import google.generativeai as genai
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
import asyncio
import nest_asyncio
import numpy as np # Untuk operasi vektor

# --- Konfigurasi ---
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN", "**********************************************")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyDaL3QpifX4FVCZoY8-koUzQsRRJMYspFU")
DOCX_DATA_PATH = os.path.join(os.path.dirname(__file__), 'ClassifiedRegulations_PDF2Docx')

# Konfigurasi logging - DIUBAH KE LEVEL DEBUG UNTUK DIAGNOSIS
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.DEBUG # <--- PENTING: UBAH INI KE INFO SETELAH DEBUGGING SELESAI
)
logger = logging.getLogger(__name__)

# Inisialisasi model Gemini
model_generation = None
embedding_available = False
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)
    try:
        # Menggunakan gemini-1.5-flash karena lebih hemat biaya dan cepat
        # Pastikan Anda memiliki akses ke model ini. Jika tidak, kembali ke gemini-pro atau gemini-2.0-flash
        model_generation = genai.GenerativeModel('gemini-1.5-flash')
        embedding_available = True
        logger.info("Model Gemini untuk generasi dan embedding berhasil diinisialisasi.")
        if model_generation and embedding_available:
            logger.debug("Model Gemini (generasi dan embedding) berhasil dibuat dan siap digunakan.")
        else:
            logger.error("Ada masalah: Model Gemini gagal dibuat.")
    except Exception as e:
        logger.error(f"Gagal menginisialisasi model Gemini (pastikan API Key valid dan terhubung ke internet): {e}")
        logger.warning("Chatbot mungkin tidak dapat menjawab pertanyaan.")
else:
    logger.warning("GEMINI_API_KEY tidak ditemukan. Chatbot mungkin tidak dapat menjawab pertanyaan.")

# --- Fungsi untuk Memuat Basis Pengetahuan ---

def extract_text_from_docx(docx_path):
    """Mengekstrak semua teks dari dokumen DOCX."""
    try:
        doc = Document(docx_path)
        full_text = []
        for para in doc.paragraphs:
            text_content = para.text.strip()
            if text_content: # Hanya tambahkan paragraf yang tidak kosong
                full_text.append(text_content)
        cleaned_text = "\n".join(full_text) # Filter non-string objects if any

        logger.debug(f"Dari '{os.path.basename(docx_path)}', panjang teks diekstrak: {len(cleaned_text)} karakter.")
        if len(cleaned_text) < 50: # Ambang batas sangat rendah untuk teks valid
            logger.warning(f"File DOCX '{os.path.basename(docx_path)}' menghasilkan teks sangat sedikit (<50 karakter). Mungkin kosong atau masalah ekstraksi.")

        return cleaned_text
    except Exception as e:
        logger.error(f"Gagal mengekstrak teks dari DOCX '{os.path.basename(docx_path)}': {e}")
        return ""

def chunk_text(text, chunk_size=700, overlap=100):
    """Membagi teks menjadi chunks dengan overlap."""
    chunks = []
    if not text:
        logger.debug("Teks kosong, tidak ada chunks yang dibuat.")
        return chunks

    start = 0
    while start < len(text):
        end = min(start + chunk_size, len(text))
        chunk = text[start:end]
        chunks.append(chunk)
        if end == len(text):
            break
        start += (chunk_size - overlap)
        # Pastikan start tidak melewati akhir teks jika chunk_size terlalu besar atau overlap terlalu besar
        if start >= len(text) and start < len(text) - (chunk_size - overlap):
            start = len(text) - (chunk_size - overlap)
            if start < 0: # Hindari indeks negatif jika teks sangat pendek
                start = 0

    return chunks


async def load_knowledge_base_with_embeddings(data_path):
    """
    Memuat semua teks dari file DOCX di dalam folder data_path
    dan subfoldernya, membagi menjadi chunks, dan menghasilkan embedding.
    """
    knowledge_base = []
    if not embedding_available:
        logger.error("Model embedding tidak tersedia. Tidak dapat memuat basis pengetahuan dengan embedding.")
        return []

    logger.debug(f"Memulai pemrosesan DOCX dari direktori: {data_path}")

    # Memastikan path DOCX_DATA_PATH ada dan dapat diakses
    if not os.path.exists(data_path):
        logger.critical(f"ERROR: Direktori data DOCX tidak ditemukan: {data_path}. Pastikan path benar.")
        return []
    if not os.path.isdir(data_path):
        logger.critical(f"ERROR: Path data DOCX bukan direktori: {data_path}.")
        return []

    found_docx_files = False
    for root, _, files in os.walk(data_path):
        for file in files:
            if file.lower().endswith(".docx"):
                found_docx_files = True
                filepath = os.path.join(root, file)
                logger.info(f"Memproses dokumen: {filepath}")
                text = extract_text_from_docx(filepath)

                if text: # Hanya lanjutkan jika ada teks yang diekstrak
                    chunks = chunk_text(text, chunk_size=700, overlap=100) # Eksperimen dengan chunk_size dan overlap
                    logger.debug(f"Dokumen '{file}' dipecah menjadi {len(chunks)} chunks.")

                    if not chunks:
                        logger.warning(f"Dokumen '{file}' tidak menghasilkan chunks. Mungkin terlalu pendek atau masalah chunking.")
                        continue # Lanjutkan ke file berikutnya jika tidak ada chunks

                    for i, chunk in enumerate(chunks):
                        try:
                            # Menghasilkan embedding untuk setiap chunk menggunakan genai.embed_content
                            embedding_response = genai.embed_content(
                                model="models/embedding-001",
                                content=chunk
                            )
                            embedding = embedding_response['embedding']
                            if embedding: # Pastikan embedding tidak kosong
                                knowledge_base.append({
                                    "filename": file,
                                    "path": filepath,
                                    "text": chunk, # Simpan chunk teks yang sebenarnya
                                    "embedding": np.array(embedding) # Konversi ke numpy array untuk perhitungan efisien
                                })
                                logger.debug(f"Embedding berhasil untuk chunk {i+1}/{len(chunks)} dari '{file}'.")
                            else:
                                logger.error(f"Embedding kosong yang diterima untuk chunk {i+1}/{len(chunks)} dari '{file}'. Masalah API atau respons kosong.")
                        except Exception as e:
                            logger.error(f"Gagal menghasilkan embedding untuk chunk {i+1}/{len(chunks)} dari '{file}': {e}")
                            logger.error(f"Chunk yang bermasalah: '{chunk[:200]}'...") # Cetak bagian awal chunk untuk diagnosis
                else:
                    logger.warning(f"Melewatkan dokumen '{file}' karena tidak ada teks yang diekstrak atau terjadi error ekstraksi.") # Tambahkan ini

    if not found_docx_files:
        logger.warning(f"Tidak ada file .docx yang ditemukan di direktori '{data_path}' atau subdirektorinya.")

    logger.info(f"Basis pengetahuan dengan embedding berhasil dimuat. Total {len(knowledge_base)} chunks.")
    if len(knowledge_base) == 0:
        logger.critical("PERHATIAN: Basis pengetahuan masih kosong! Pastikan folder DOCX ada dan berisi file yang valid dan teks dapat diekstrak.")
    return knowledge_base

# --- Fungsi Pencarian Basis Pengetahuan Semantik ---

def cosine_similarity(vec1, vec2):
    """Menghitung cosine similarity antara dua vektor."""
    # Menangani kasus vektor nol untuk menghindari pembagian dengan nol
    norm_vec1 = np.linalg.norm(vec1)
    norm_vec2 = np.linalg.norm(vec2)
    if norm_vec1 == 0 or norm_vec2 == 0:
        return 0.0 # Atau berikan nilai sangat kecil jika diinginkan

    return np.dot(vec1, vec2) / (norm_vec1 * norm_vec2)

async def search_knowledge_base_semantic(query, knowledge_base, top_n=5):
    """
    Mencari basis pengetahuan untuk chunk dokumen yang paling relevan
    secara semantik menggunakan embedding.
    """
    if not embedding_available:
        logger.error("Model embedding tidak tersedia untuk pencarian semantik saat query.")
        return "Model embedding tidak tersedia untuk pencarian semantik."
    if not knowledge_base:
        logger.warning("Basis pengetahuan kosong atau belum dimuat saat query. Tidak dapat melakukan pencarian.")
        return "Basis pengetahuan kosong atau belum dimuat. Tidak ada informasi yang bisa dicari."

    try:
        query_embedding_response = genai.embed_content(
            model="models/embedding-001",
            content=query
        )
        query_embedding = np.array(query_embedding_response['embedding'])
        logger.debug(f"Embedding query berhasil dibuat (panjang: {len(query_embedding)}).")
    except Exception as e:
        logger.error(f"Gagal menghasilkan embedding untuk query '{query}': {e}")
        return "Terjadi kesalahan saat memproses pertanyaan Anda."

    scores = []
    for doc_chunk in knowledge_base:
        if 'embedding' in doc_chunk and doc_chunk['embedding'] is not None:
            similarity_score = cosine_similarity(query_embedding, doc_chunk['embedding'])
            scores.append((similarity_score, doc_chunk))
        else:
            logger.debug(f"Melewatkan chunk dari '{doc_chunk.get('filename', 'N/A')}' karena tidak memiliki embedding.")

    # Urutkan berdasarkan skor kesamaan (tertinggi di atas)
    scores.sort(key=lambda x: x[0], reverse=True)

    context_text = []

    logger.debug(f"Top {top_n} skor kesamaan: {[s[0] for s in scores[:top_n]]}")

    # Ambil chunk top N yang relevan
    for score, doc_chunk in scores:
        if score > 0.2: # Turunkan threshold untuk mendapatkan lebih banyak konteks relevan
            context_text.append(f"### Dokumen: {os.path.basename(doc_chunk['filename'])}\n{doc_chunk['text']}")
            if len(context_text) >= top_n:
                break
        else:
            logger.debug(f"Berhenti menambahkan konteks karena skor kesamaan ({score:.2f}) di bawah ambang batas (0.2).")
            break

    if not context_text:
        logger.info("Tidak ada informasi relevan yang ditemukan dalam basis pengetahuan (setelah pencarian semantik).")
        return "Tidak ada informasi relevan yang ditemukan dalam basis pengetahuan."

    logger.debug(f"Jumlah chunk konteks yang akan diteruskan: {len(context_text)}")
    return "\n\n".join(context_text)

# --- Handler Perintah Telegram ---

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Mengirim pesan sambutan saat perintah /start diterima."""
    await update.message.reply_text(
        'Halo! Saya adalah chatbot peraturan Anda, Junwar Bot. '
        'Saya dapat menjawab pertanyaan berdasarkan dokumen peraturan yang telah Anda sediakan. '
        'Silakan ajukan pertanyaan Anda!'
    )

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Menangani pesan teks dari pengguna dan memberikan jawaban."""
    user_query = update.message.text
    logger.info(f"Menerima pertanyaan dari {update.effective_user.full_name}: {user_query}")

    if not model_generation or not embedding_available:
        await update.message.reply_text(
            "Maaf, model AI tidak dapat diinisialisasi karena kunci API Gemini tidak ditemukan "
            "atau model embedding tidak tersedia. Mohon pastikan variabel lingkungan GEMINI_API_KEY telah diatur."
        )
        return

    await update.message.reply_text("Mencari informasi dan merumuskan jawaban...")

    # Cari konteks relevan dari basis pengetahuan menggunakan pencarian semantik
    relevant_context = await search_knowledge_base_semantic(user_query, context.bot_data['knowledge_base'], top_n=8)

    # Evaluasi Konteks yang Diteruskan ke Gemini
    if len(relevant_context) > 1000:
        logger.info(f"Konteks relevan yang diteruskan ke Gemini (1000 char pertama):\n{relevant_context[:1000]}...")
    else:
        logger.info(f"Konteks relevan yang diteruskan ke Gemini:\n{relevant_context}")

    # Buat prompt untuk model Gemini (Peningkatan Prompt Engineering)
    prompt = (
        f"Anda adalah asisten ahli peraturan yang membantu menjawab pertanyaan berdasarkan dokumen peraturan yang diberikan. "
        f"INSTRUKSI PENTING:\n"
        f"1. Gunakan informasi dari konteks yang diberikan untuk menjawab pertanyaan secara lengkap dan detail\n"
        f"2. Jika ada informasi yang relevan dalam konteks, berikan jawaban yang komprehensif\n"
        f"3. Sebutkan pasal, ayat, atau bagian spesifik dari peraturan jika tersedia\n"
        f"4. Berikan referensi nama dokumen/peraturan yang relevan\n"
        f"5. Jika informasi tidak lengkap, jelaskan apa yang tersedia dan sarankan untuk mencari informasi tambahan\n"
        f"6. Jawab dalam bahasa Indonesia yang jelas dan terstruktur\n\n"
        f"Konteks dari dokumen peraturan:\n{relevant_context}\n\n"
        f"Pertanyaan: {user_query}\n\n"
        f"Jawaban lengkap berdasarkan peraturan:"
    )

    try:
        # Panggil Gemini API
        response = await model_generation.generate_content_async(prompt)

        answer = "Maaf, saya tidak dapat merumuskan jawaban berdasarkan informasi yang tersedia."
        if response.candidates and response.candidates[0].content.parts:
            answer = response.candidates[0].content.parts[0].text
        else:
            # Periksa jika ada prompt feedback atau safety ratings yang memblokir
            if response.prompt_feedback:
                logger.warning(f"Prompt feedback dari Gemini API: {response.prompt_feedback}")
                if response.prompt_feedback.block_reason:
                    answer = f"Maaf, permintaan Anda diblokir oleh sistem keamanan AI karena: {response.prompt_feedback.block_reason.name}."
            if response.candidates and response.candidates[0].finish_reason:
                logger.warning(f"Finish reason dari Gemini API: {response.candidates[0].finish_reason}")
                if response.candidates[0].finish_reason.name == 'SAFETY':
                     answer = "Maaf, jawaban tidak dapat dihasilkan karena alasan keamanan konten."
                elif response.candidates[0].finish_reason.name == 'STOP':
                    answer = "Maaf, model AI menghentikan generasi jawaban secara prematur." # Contoh handle finish_reason lain

        logger.info(f"Menghasilkan jawaban: {answer}")
        await update.message.reply_text(answer)
    except Exception as e:
        logger.error(f"Gagal memanggil Gemini API atau menghasilkan konten untuk pertanyaan '{user_query}': {e}")
        await update.message.reply_text(
            "Maaf, saya mengalami masalah saat memproses permintaan Anda. "
            "Mohon coba lagi nanti atau periksa log untuk detailnya."
        )

# --- Fungsi Utama untuk Menjalankan Bot ---

async def main() -> None:
    """Menjalankan bot."""
    application = Application.builder().token(TELEGRAM_BOT_TOKEN).build()

    logger.info("Memuat basis pengetahuan dan menghasilkan embedding...")
    application.bot_data['knowledge_base'] = await load_knowledge_base_with_embeddings(DOCX_DATA_PATH)
    logger.info(f"Basis pengetahuan dengan embedding selesai dimuat. Total {len(application.bot_data['knowledge_base'])} chunks.")

    application.add_handler(CommandHandler("start", start))
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))

    logger.info("Bot Telegram dimulai dan siap menerima pesan...")
    await application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    nest_asyncio.apply() # Memungkinkan event loop bersarang, berguna di lingkungan interaktif
    try:
        asyncio.run(main())
    except Exception as e:
        logger.critical(f"Kesalahan fatal saat menjalankan bot: {e}")